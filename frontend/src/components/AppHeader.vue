<template>
  <header class="header">
    <div class="container">
      <div class="header-content">
        <div class="logo-section">
          <h1 class="text-xl font-bold text-primary">{{ $t('app.title') }}</h1>
        </div>
        
        <nav v-if="isAuthenticated" class="nav">
          <RouterLink to="/" class="nav-link">{{ $t('navigation.home') }}</RouterLink>
          <RouterLink to="/about" class="nav-link">{{ $t('navigation.about') }}</RouterLink>
        </nav>
        
        <div class="header-actions">
          <span v-if="user && isAuthenticated" class="user-info text-sm text-secondary">
            {{ $t('app.welcome', { username: user.username }) }}
          </span>
          
          <button 
            @click="toggleLanguage" 
            class="btn btn-secondary"
            :title="$t('language.change')"
          >
            {{ getCurrentLanguageInfo()?.flag }}
          </button>
          
          <button 
            @click="toggleTheme" 
            class="btn btn-secondary"
            :title="$t('theme.toggle')"
          >
            {{ theme === 'light' ? '🌙' : '☀️' }}
          </button>
          
          <button 
            v-if="isAuthenticated"
            @click="logout" 
            class="btn btn-secondary"
            :disabled="isLoggingOut"
          >
            {{ isLoggingOut ? $t('auth.logout.signingOut') : $t('auth.logout.signOut') }}
          </button>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
import { useTheme } from '../composables/useTheme'
import { useAuth } from '../composables/useAuth'
import { useLanguage } from '../composables/useLanguage'

const { toggleTheme, theme } = useTheme()
const { isAuthenticated, user, logout, isLoggingOut } = useAuth()
const { toggleLanguage, getCurrentLanguageInfo } = useLanguage()
</script>

<style scoped>
.header {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
}

.logo-section h1 {
  color: var(--color-primary-500);
}

.nav {
  display: flex;
  gap: var(--space-6);
}

.nav-link {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
  text-decoration: none;
}

.nav-link:hover {
  color: var(--color-text-primary);
}

.nav-link.router-link-active {
  color: var(--color-primary-500);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.user-info {
  white-space: nowrap;
}

@media (max-width: 768px) {
  .header-content {
    flex-wrap: wrap;
    gap: var(--space-4);
  }
  
  .nav {
    order: 3;
    width: 100%;
    justify-content: center;
  }
  
  .user-info {
    display: none;
  }
}
</style>
