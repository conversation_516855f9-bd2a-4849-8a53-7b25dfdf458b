<script setup lang="ts">
import { onMounted } from 'vue'
import { RouterLink, RouterView } from 'vue-router'
import { useTheme } from './composables/useTheme'
import { useAuth } from './composables/useAuth'

const { initTheme, toggleTheme, theme } = useTheme()
const { initAuth, isAuthenticated, user, logout, isLoggingOut } = useAuth()

onMounted(async () => {
  initTheme()
  await initAuth()
})
</script>

<template>
  <div class="app-layout">
    <header v-if="isAuthenticated" class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo-section">
            <h1 class="text-xl font-bold text-primary">Event Manager</h1>
          </div>

          <nav class="nav">
            <RouterLink to="/" class="nav-link">Home</RouterLink>
            <RouterLink to="/about" class="nav-link">About</RouterLink>
          </nav>

          <div class="header-actions">
            <span v-if="user" class="user-info text-sm text-secondary">
              Welcome, {{ user.username }}
            </span>
            <button @click="toggleTheme" class="btn btn-secondary">
              {{ theme === 'light' ? '🌙' : '☀️' }}
            </button>
            <button @click="logout" class="btn btn-secondary" :disabled="isLoggingOut">
              {{ isLoggingOut ? 'Signing out...' : 'Sign Out' }}
            </button>
          </div>
        </div>
      </div>
    </header>

    <main class="main-content" :class="{ 'no-header': !isAuthenticated }">
      <RouterView />
    </main>
  </div>
</template>

<style scoped>
.header {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
}

.logo-section h1 {
  color: var(--color-primary-500);
}

.nav {
  display: flex;
  gap: var(--space-6);
}

.nav-link {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
  text-decoration: none;
}

.nav-link:hover {
  color: var(--color-text-primary);
}

.nav-link.router-link-active {
  color: var(--color-primary-500);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.user-info {
  white-space: nowrap;
}

.main-content.no-header {
  padding: 0;
}
</style>
