<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { RouterView, useRoute } from 'vue-router'
import { useTheme } from './composables/useTheme'
import { useAuth } from './composables/useAuth'
import AppHeader from './components/AppHeader.vue'

const { initTheme } = useTheme()
const { initAuth, isAuthenticated } = useAuth()
const route = useRoute()

// Don't add padding to login page
const shouldAddPadding = computed(() => {
  return route.name !== 'login' && isAuthenticated.value
})

onMounted(async () => {
  initTheme()
  await initAuth()
})
</script>

<template>
  <div class="app-layout">
    <AppHeader />

    <main class="main-content" :class="{ 'with-padding': shouldAddPadding }">
      <RouterView />
    </main>
  </div>
</template>

<style scoped>
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}
</style>
