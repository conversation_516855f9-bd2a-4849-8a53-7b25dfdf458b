import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { setLocale, getCurrentLocale, type SupportedLocale } from '../i18n'

export function useLanguage() {
  const { locale } = useI18n()

  const currentLanguage = computed(() => getCurrentLocale())
  
  const availableLanguages = [
    { code: 'ro', name: 'Rom<PERSON><PERSON>', flag: '🇷🇴' },
    { code: 'en', name: 'English', flag: '🇺🇸' }
  ] as const

  const changeLanguage = (newLocale: SupportedLocale) => {
    setLocale(newLocale)
  }

  const toggleLanguage = () => {
    const newLocale = currentLanguage.value === 'ro' ? 'en' : 'ro'
    changeLanguage(newLocale)
  }

  const getCurrentLanguageInfo = () => {
    return availableLanguages.find(lang => lang.code === currentLanguage.value)
  }

  return {
    currentLanguage,
    availableLanguages,
    changeLanguage,
    toggleLanguage,
    getCurrentLanguageInfo
  }
}
