import { ref, computed, readonly } from 'vue'
import { useRouter } from 'vue-router'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { api } from '../services/api'

export interface User {
  id: string
  username: string
  email: string
  role: 'admin' | 'user'
  lastLogin?: string
  createdAt?: string
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResponse {
  success: boolean
  message: string
  data: {
    user: User
    accessToken: string
  }
}

const accessToken = ref<string | null>(localStorage.getItem('accessToken'))
const user = ref<User | null>(null)
const isVerifying = ref(false)

export function useAuth() {
  const router = useRouter()
  const queryClient = useQueryClient()

  // Computed properties
  const isAuthenticated = computed(() => !!accessToken.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // Set authentication data
  const setAuth = (authData: { user: User; accessToken: string }) => {
    user.value = authData.user
    accessToken.value = authData.accessToken
    localStorage.setItem('accessToken', authData.accessToken)
  }

  // Clear authentication data
  const clearAuth = () => {
    user.value = null
    accessToken.value = null
    localStorage.removeItem('accessToken')
    queryClient.clear()
  }

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginCredentials): Promise<AuthResponse> => {
      return api.post('/auth/login', credentials)
    },
    onSuccess: (data) => {
      setAuth(data.data)
      router.push('/')
    },
    onError: (error: any) => {
      console.error('Login failed:', error)
    },
    onSettled: () => {
      // This ensures the loading state is always reset
    },
  })

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      return api.post('/auth/logout')
    },
    onSuccess: () => {
      clearAuth()
      router.push('/login')
    },
    onError: () => {
      // Clear auth even if logout request fails
      clearAuth()
      router.push('/login')
    },
  })

  // Verify authentication function
  const verifyAuth = async () => {
    if (!accessToken.value) {
      return false
    }

    isVerifying.value = true
    try {
      const response = await api.get('/auth/verify', {
        headers: {
          Authorization: `Bearer ${accessToken.value}`,
        },
      })
      user.value = (response as AuthResponse).data.user
      return true
    } catch (error) {
      clearAuth()
      return false
    } finally {
      isVerifying.value = false
    }
  }

  // Refresh token function
  const refreshToken = async () => {
    try {
      const response = await api.post('/auth/refresh')
      setAuth((response as AuthResponse).data)
      return true
    } catch (error) {
      clearAuth()
      return false
    }
  }

  // Login function
  const login = (credentials: LoginCredentials) => {
    return loginMutation.mutate(credentials)
  }

  // Logout function
  const logout = () => {
    return logoutMutation.mutate()
  }

  // Initialize auth on app start
  const initAuth = async () => {
    if (accessToken.value) {
      // Token exists, verify it
      const verified = await verifyAuth()
      if (!verified) {
        // Token is invalid, try to refresh
        const refreshed = await refreshToken()
        if (!refreshed) {
          clearAuth()
        }
      }
    }
  }

  // Reset login state
  const resetLoginState = () => {
    loginMutation.reset()
  }

  return {
    // State
    user: readonly(user),
    accessToken: readonly(accessToken),
    isAuthenticated,
    isAdmin,

    // Loading states
    isLoggingIn: computed(() => loginMutation.isPending.value),
    isLoggingOut: computed(() => logoutMutation.isPending.value),
    isVerifying: readonly(isVerifying),

    // Error states
    loginError: computed(() => loginMutation.error.value),

    // Actions
    login,
    logout,
    refreshToken,
    initAuth,
    clearAuth,
    verifyAuth,
    resetLoginState,
  }
}
