<template>
  <div class="login-container">
    <div class="login-card card">
      <div class="login-header">
        <h1 class="text-2xl font-bold text-center mb-2">Welcome Back</h1>
        <p class="text-secondary text-center mb-6">Sign in to Event Manager</p>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group mb-4">
          <label for="username" class="form-label">Username</label>
          <input
            id="username"
            v-model="credentials.username"
            type="text"
            class="input"
            :class="{ 'input-error': errors.username }"
            placeholder="Enter your username"
            required
            autocomplete="username"
            :disabled="isLoggingIn"
          />
          <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
        </div>

        <div class="form-group mb-6">
          <label for="password" class="form-label">Password</label>
          <div class="password-input-container">
            <input
              id="password"
              v-model="credentials.password"
              :type="showPassword ? 'text' : 'password'"
              class="input"
              :class="{ 'input-error': errors.password }"
              placeholder="Enter your password"
              required
              autocomplete="current-password"
              :disabled="isLoggingIn"
            />
            <button
              type="button"
              @click="togglePasswordVisibility"
              class="password-toggle"
              :disabled="isLoggingIn"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
          <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
        </div>

        <div v-if="loginError" class="error-banner mb-4">
          <p class="text-sm">
            {{ getErrorMessage(loginError) }}
          </p>
        </div>

        <button
          type="submit"
          class="btn btn-primary w-full"
          :disabled="isLoggingIn || !isFormValid"
        >
          <span v-if="isLoggingIn" class="loading-spinner"></span>
          {{ isLoggingIn ? 'Signing in...' : 'Sign In' }}
        </button>
      </form>

      <div class="login-footer mt-6">
        <div class="demo-credentials">
          <p class="text-xs text-muted text-center">Demo Credentials:</p>
          <p class="text-xs text-muted text-center">Username: <strong>root</strong> | Password: <strong>root</strong></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'

const router = useRouter()
const { login, isLoggingIn, loginError, isAuthenticated } = useAuth()

// Form state
const credentials = ref({
  username: '',
  password: ''
})

const showPassword = ref(false)
const errors = ref({
  username: '',
  password: ''
})

// Computed properties
const isFormValid = computed(() => {
  return credentials.value.username.trim() !== '' && 
         credentials.value.password.trim() !== '' &&
         !errors.value.username && 
         !errors.value.password
})

// Methods
const validateForm = () => {
  errors.value = { username: '', password: '' }
  
  if (!credentials.value.username.trim()) {
    errors.value.username = 'Username is required'
  }
  
  if (!credentials.value.password.trim()) {
    errors.value.password = 'Password is required'
  } else if (credentials.value.password.length < 3) {
    errors.value.password = 'Password must be at least 3 characters'
  }
  
  return !errors.value.username && !errors.value.password
}

const handleLogin = async () => {
  if (!validateForm()) return
  
  try {
    await login(credentials.value)
  } catch (error) {
    console.error('Login failed:', error)
  }
}

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const getErrorMessage = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message
  }
  if (error?.message) {
    return error.message
  }
  return 'An unexpected error occurred. Please try again.'
}

// Redirect if already authenticated
onMounted(() => {
  if (isAuthenticated.value) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-secondary-50) 100%);
  padding: var(--space-4);
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: var(--space-8);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-2);
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-1);
  color: var(--color-text-muted);
  transition: color var(--transition-fast);
}

.password-toggle:hover:not(:disabled) {
  color: var(--color-text-secondary);
}

.password-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-error {
  border-color: var(--color-error-500);
}

.input-error:focus {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

.error-message {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-error-500);
  margin-top: var(--space-1);
}

.error-banner {
  background-color: var(--color-error-50);
  border: 1px solid var(--color-error-200);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  color: var(--color-error-600);
}

.w-full {
  width: 100%;
}

.demo-credentials {
  background-color: var(--color-surface-secondary);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  margin-top: var(--space-4);
}

@media (max-width: 480px) {
  .login-card {
    padding: var(--space-6);
  }
}
</style>
