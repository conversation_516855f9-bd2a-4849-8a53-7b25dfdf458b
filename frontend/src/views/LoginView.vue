<template>
  <div class="login-container">
    <div class="login-card card">
      <div class="login-header">
        <h1 class="text-2xl font-bold text-center mb-2">{{ $t('auth.login.title') }}</h1>
        <p class="text-secondary text-center mb-6">{{ $t('auth.login.subtitle') }}</p>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group mb-4">
          <label for="username" class="form-label">{{ $t('auth.login.username') }}</label>
          <input
            id="username"
            v-model="credentials.username"
            type="text"
            class="input"
            :class="{ 'input-error': errors.username }"
            :placeholder="$t('auth.login.usernamePlaceholder')"
            required
            autocomplete="username"
            :disabled="isLoggingIn"
          />
          <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
        </div>

        <div class="form-group mb-6">
          <label for="password" class="form-label">{{ $t('auth.login.password') }}</label>
          <div class="password-input-container">
            <input
              id="password"
              v-model="credentials.password"
              :type="showPassword ? 'text' : 'password'"
              class="input"
              :class="{ 'input-error': errors.password }"
              :placeholder="$t('auth.login.passwordPlaceholder')"
              required
              autocomplete="current-password"
              :disabled="isLoggingIn"
            />
            <button
              type="button"
              @click="togglePasswordVisibility"
              class="password-toggle"
              :disabled="isLoggingIn"
              :title="showPassword ? $t('auth.login.hidePassword') : $t('auth.login.showPassword')"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
          <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
        </div>

        <div v-if="loginError" class="error-banner mb-4">
          <p class="text-sm">
            {{ getErrorMessage(loginError) }}
          </p>
        </div>

        <button
          type="submit"
          class="btn btn-primary w-full"
          :disabled="isLoggingIn || !isFormValid"
        >
          <span v-if="isLoggingIn" class="loading-spinner"></span>
          {{ isLoggingIn ? $t('auth.login.signingIn') : $t('auth.login.signIn') }}
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuth } from '../composables/useAuth'

const router = useRouter()
const { t } = useI18n()
const { login, isLoggingIn, loginError, isAuthenticated } = useAuth()

// Form state
const credentials = ref({
  username: '',
  password: '',
})

const showPassword = ref(false)
const errors = ref({
  username: '',
  password: '',
})

// Computed properties
const isFormValid = computed(() => {
  return (
    credentials.value.username.trim() !== '' &&
    credentials.value.password.trim() !== '' &&
    !errors.value.username &&
    !errors.value.password
  )
})

// Methods
const validateForm = () => {
  errors.value = { username: '', password: '' }

  if (!credentials.value.username.trim()) {
    errors.value.username = t('auth.login.usernameRequired')
  } else if (credentials.value.username.length < 3) {
    errors.value.username = t('auth.login.usernameMinLength')
  }

  if (!credentials.value.password.trim()) {
    errors.value.password = t('auth.login.passwordRequired')
  } else if (credentials.value.password.length < 3) {
    errors.value.password = t('auth.login.passwordMinLength')
  }

  return !errors.value.username && !errors.value.password
}

const handleLogin = () => {
  if (!validateForm()) return
  login(credentials.value)
}

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const getErrorMessage = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message
  }
  if (error?.message) {
    return error.message
  }
  return t('auth.login.unexpectedError')
}

// Redirect if already authenticated
onMounted(() => {
  if (isAuthenticated.value) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  min-height: calc(100vh - 80px); /* Account for header height */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-secondary-50) 100%);
  padding: var(--space-4);
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: var(--space-8);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-2);
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-1);
  color: var(--color-text-muted);
  transition: color var(--transition-fast);
}

.password-toggle:hover:not(:disabled) {
  color: var(--color-text-secondary);
}

.password-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-error {
  border-color: var(--color-error-500);
}

.input-error:focus {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

.error-message {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-error-500);
  margin-top: var(--space-1);
}

.error-banner {
  background-color: var(--color-error-50);
  border: 1px solid var(--color-error-200);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  color: var(--color-error-600);
}

.w-full {
  width: 100%;
}

@media (max-width: 480px) {
  .login-card {
    padding: var(--space-6);
  }
}
</style>
