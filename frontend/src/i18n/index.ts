import { createI18n } from 'vue-i18n'
import ro from './locales/ro.json'
import en from './locales/en.json'

export type SupportedLocale = 'ro' | 'en'

// Get user's preferred language
const getDefaultLocale = (): SupportedLocale => {
  // Check localStorage first
  const stored = localStorage.getItem('locale') as SupportedLocale
  if (stored && ['ro', 'en'].includes(stored)) {
    return stored
  }

  // Check browser language
  const browserLang = navigator.language.split('-')[0]
  if (browserLang === 'ro' || browserLang === 'en') {
    return browserLang as SupportedLocale
  }

  // Default to Romanian
  return 'ro'
}

const i18n = createI18n({
  legacy: false,
  locale: getDefaultLocale(),
  fallbackLocale: 'ro',
  messages: {
    ro,
    en
  },
  globalInjection: true
})

export default i18n

// Helper function to change locale
export const setLocale = (locale: SupportedLocale) => {
  i18n.global.locale.value = locale
  localStorage.setItem('locale', locale)
  document.documentElement.lang = locale
}

// Helper function to get current locale
export const getCurrentLocale = (): SupportedLocale => {
  return i18n.global.locale.value as SupportedLocale
}
