{"name": "event_manager", "version": "1.0.0", "description": "Full-stack Event Manager application with Vue 3 and Node.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["vue", "nodejs", "express", "mongodb", "typescript"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.1.2"}}