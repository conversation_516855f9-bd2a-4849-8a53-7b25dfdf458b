# Event Manager

A full-stack event management application built with Vue 3, Node.js, Express, and MongoDB.

## 🚀 Tech Stack

### Frontend
- **Vue 3** with TypeScript
- **Vue Router** for routing
- **TanStack Query** for state management and API calls
- **CSS3 Variables** with scalable theme architecture
- **Vite** for fast development and building

### Backend
- **Node.js** with Express
- **TypeScript** for type safety
- **MongoDB** with Mongoose ODM
- **JWT** for authentication (ready to implement)
- **Helmet** for security
- **CORS** for cross-origin requests

## 📁 Project Structure

```
event_manager/
├── frontend/                 # Vue 3 application
│   ├── src/
│   │   ├── components/      # Reusable Vue components
│   │   ├── views/           # Page components
│   │   ├── router/          # Vue Router configuration
│   │   ├── composables/     # Vue composables (useTheme, etc.)
│   │   ├── services/        # API service layer
│   │   ├── plugins/         # Vue plugins (TanStack Query)
│   │   └── styles/          # CSS architecture
│   │       ├── themes/      # Theme variables (light/dark)
│   │       ├── utils/       # Utility classes and reset
│   │       └── components/  # Component-specific styles
│   └── package.json
├── backend/                  # Node.js/Express API
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── models/          # Mongoose models
│   │   ├── routes/          # Express routes
│   │   ├── middleware/      # Custom middleware
│   │   ├── config/          # Database and app configuration
│   │   └── utils/           # Utility functions
│   └── package.json
└── package.json             # Root package.json for scripts
```

## 🛠️ Setup Instructions

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn

### Installation

1. **Clone and install dependencies:**
   ```bash
   npm run install:all
   ```

2. **Set up environment variables:**
   
   **Backend (.env):**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your MongoDB URI and other settings
   ```
   
   **Frontend (.env):**
   ```bash
   cp frontend/.env.example frontend/.env
   # Edit frontend/.env if needed (API URL is set to localhost:3001 by default)
   ```

3. **Start MongoDB:**
   ```bash
   # If using local MongoDB
   mongod
   
   # Or use MongoDB Atlas (update MONGODB_URI in backend/.env)
   ```

4. **Start development servers:**
   ```bash
   npm run dev
   ```
   
   This will start both frontend (http://localhost:5173) and backend (http://localhost:3001) concurrently.

### Individual Commands

```bash
# Start only frontend
npm run dev:frontend

# Start only backend
npm run dev:backend

# Build for production
npm run build

# Start production server (after build)
npm start
```

## 🎨 Theme System

The application includes a comprehensive CSS3 variables-based theme system:

- **Light/Dark themes** with automatic system preference detection
- **Scalable color palette** with semantic naming
- **Typography scale** with consistent font sizes and weights
- **Spacing system** using consistent units
- **Component utilities** for rapid development

Toggle between themes using the theme button in the header.

## 📡 API Endpoints

### Health Check
- `GET /api/health` - API health status

*More endpoints will be added as you define your requirements.*

## 🔧 Development

### Frontend Development
- Hot reload enabled with Vite
- TypeScript support
- ESLint and Prettier configured
- TanStack Query for efficient data fetching

### Backend Development
- Nodemon for auto-restart on changes
- TypeScript compilation
- MongoDB connection with Mongoose
- Error handling middleware
- CORS configured for frontend

## 📝 Next Steps

The basic setup is complete! You can now:

1. **Define your database models** in `backend/src/models/`
2. **Create API routes** in `backend/src/routes/`
3. **Build Vue components** in `frontend/src/components/`
4. **Add new views** in `frontend/src/views/`
5. **Implement authentication** using JWT
6. **Add form validation** and error handling
7. **Create tests** for both frontend and backend

## 🤝 Contributing

1. Create feature branches
2. Follow TypeScript best practices
3. Use the established CSS theme system
4. Write tests for new features
5. Update documentation as needed

## 📄 License

ISC License
