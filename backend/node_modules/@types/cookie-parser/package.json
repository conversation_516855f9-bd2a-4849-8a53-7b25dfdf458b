{"name": "@types/cookie-parser", "version": "1.4.8", "description": "TypeScript definitions for cookie-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie-parser"}, "scripts": {}, "dependencies": {}, "peerDependencies": {"@types/express": "*"}, "typesPublisherContentHash": "407ca12aad2175b5f5fe7c379897557f353e9b7dfaf47b3eb69a86f1d6b4f251", "typeScriptVersion": "5.0"}