{"name": "@types/jsdom", "version": "21.1.7", "description": "TypeScript definitions for jsdom", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsdom", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu", "url": "https://github.com/leonard-thieu"}, {"name": "<PERSON>", "githubUsername": "palmfjord", "url": "https://github.com/palmfjord"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jsdom"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/tough-cookie": "*", "parse5": "^7.0.0"}, "typesPublisherContentHash": "ff2b3302adf7f1ae40db6101f0c6d5f7ba972ee3864ae371e975f9545d93d8bb", "typeScriptVersion": "4.7"}