"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = getDomainWithoutSuffix;
/**
 * Return the part of domain without suffix.
 *
 * Example: for domain 'foo.com', the result would be 'foo'.
 */
function getDomainWithoutSuffix(domain, suffix) {
    // Note: here `domain` and `suffix` cannot have the same length because in
    // this case we set `domain` to `null` instead. It is thus safe to assume
    // that `suffix` is shorter than `domain`.
    return domain.slice(0, -suffix.length - 1);
}
//# sourceMappingURL=domain-without-suffix.js.map