{"version": 3, "file": "extract-hostname.js", "sourceRoot": "", "sources": ["../../../src/extract-hostname.ts"], "names": [], "mappings": ";;AAIA,kCAqKC;AAzKD;;;GAGG;AACH,SAAwB,eAAe,CACrC,GAAW,EACX,kBAA2B;IAE3B,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,GAAW,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAI,QAAQ,GAAG,KAAK,CAAC;IAErB,wEAAwE;IACxE,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,gCAAgC;QAChC,IAAI,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,sBAAsB;QACtB,OAAO,KAAK,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;YACzD,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,uBAAuB;QACvB,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;YACxD,GAAG,IAAI,CAAC,CAAC;QACX,CAAC;QAED,eAAe;QACf,IACE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS;YACtC,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,EAC1C,CAAC;YACD,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;aAAM,CAAC;YACN,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC3B,qEAAqE;gBACrE,oEAAoE;gBACpE,kDAAkD;gBAClD,MAAM,YAAY,GAAG,eAAe,GAAG,KAAK,CAAC;gBAC7C,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACjC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACrC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACrC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACrC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAErC,IACE,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS,EACpB,CAAC;oBACD,QAAQ;gBACV,CAAC;qBAAM,IACL,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS,EACpB,CAAC;oBACD,OAAO;gBACT,CAAC;qBAAM,IACL,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS,EACpB,CAAC;oBACD,MAAM;gBACR,CAAC;qBAAM,IACL,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS,EACpB,CAAC;oBACD,KAAK;gBACP,CAAC;qBAAM,CAAC;oBACN,6BAA6B;oBAC7B,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;wBAChD,MAAM,aAAa,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;wBAC7C,IACE,CAAC,CACC,CACE,CAAC,aAAa,IAAI,EAAE,IAAI,aAAa,IAAI,GAAG,CAAC,IAAI,SAAS;4BAC1D,CAAC,aAAa,IAAI,EAAE,IAAI,aAAa,IAAI,EAAE,CAAC,IAAI,SAAS;4BACzD,aAAa,KAAK,EAAE,IAAI,MAAM;4BAC9B,aAAa,KAAK,EAAE,IAAI,MAAM;4BAC9B,aAAa,KAAK,EAAE,CACrB,CAAC,MAAM;yBACT,EACD,CAAC;4BACD,OAAO,IAAI,CAAC;wBACd,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,mCAAmC;gBACnC,KAAK,GAAG,eAAe,GAAG,CAAC,CAAC;gBAC5B,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;oBAC9C,KAAK,IAAI,CAAC,CAAC;gBACb,CAAC;YACH,CAAC;QACH,CAAC;QAED,wEAAwE;QACxE,uEAAuE;QACvE,4CAA4C;QAC5C,IAAI,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC3B,IAAI,qBAAqB,GAAG,CAAC,CAAC,CAAC;QAC/B,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,GAAW,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACvC,IACE,IAAI,KAAK,EAAE,IAAI,MAAM;gBACrB,IAAI,KAAK,EAAE,IAAI,MAAM;gBACrB,IAAI,KAAK,EAAE,CAAC,MAAM;cAClB,CAAC;gBACD,GAAG,GAAG,CAAC,CAAC;gBACR,MAAM;YACR,CAAC;iBAAM,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;gBACvB,MAAM;gBACN,iBAAiB,GAAG,CAAC,CAAC;YACxB,CAAC;iBAAM,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;gBACvB,MAAM;gBACN,qBAAqB,GAAG,CAAC,CAAC;YAC5B,CAAC;iBAAM,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;gBACvB,MAAM;gBACN,WAAW,GAAG,CAAC,CAAC;YAClB,CAAC;iBAAM,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;gBACpC,QAAQ,GAAG,IAAI,CAAC;YAClB,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,IACE,iBAAiB,KAAK,CAAC,CAAC;YACxB,iBAAiB,GAAG,KAAK;YACzB,iBAAiB,GAAG,GAAG,EACvB,CAAC;YACD,KAAK,GAAG,iBAAiB,GAAG,CAAC,CAAC;QAChC,CAAC;QAED,wBAAwB;QACxB,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;YAC3C,IAAI,qBAAqB,KAAK,CAAC,CAAC,EAAE,CAAC;gBACjC,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAC;YACnE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,WAAW,GAAG,KAAK,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;YAC1E,mBAAmB;YACnB,GAAG,GAAG,WAAW,CAAC;QACpB,CAAC;IACH,CAAC;IAED,qBAAqB;IACrB,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;QACnE,GAAG,IAAI,CAAC,CAAC;IACX,CAAC;IAED,MAAM,QAAQ,GACZ,KAAK,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAElE,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC"}