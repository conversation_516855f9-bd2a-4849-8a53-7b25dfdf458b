{"name": "tldts", "version": "6.1.86", "description": "Library to work against complex domain names, subdomains and URIs.", "author": {"name": "<PERSON><PERSON><PERSON>"}, "contributors": ["<PERSON> <alexe<PERSON><PERSON>ahoodot<PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://github.com/remusao/tldts#readme", "bugs": {"url": "https://github.com/remusao/tldts/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/remusao/tldts.git"}, "main": "dist/cjs/index.js", "module": "dist/es6/index.js", "types": "dist/types/index.d.ts", "files": ["dist", "src", "index.ts"], "bin": {"tldts": "bin/cli.js"}, "scripts": {"clean": "rimraf dist coverage", "build": "tsc --build ./tsconfig.json", "bundle": "tsc --build ./tsconfig.bundle.json && rollup --config ./rollup.config.mjs", "prepack": "yarn run bundle", "test": "nyc mocha --config ../../.mocharc.cjs"}, "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^12.1.0", "@types/chai": "^4.2.18", "@types/mocha": "^10.0.0", "@types/node": "^22.0.0", "chai": "^4.4.1", "mocha": "^11.0.1", "nyc": "^17.0.0", "rimraf": "^5.0.1", "rollup": "^4.1.0", "rollup-plugin-sourcemaps": "^0.6.1", "tldts-tests": "^6.1.86", "typescript": "^5.0.4"}, "dependencies": {"tldts-core": "^6.1.86"}, "keywords": ["tld", "sld", "domain", "subdomain", "subdomain", "hostname", "browser", "uri", "url", "domain name", "public suffix", "url parsing", "typescript"], "gitHead": "94251baa0e4ee46df6fd06fcd3749fcdf9b14ebc"}