import { Request } from 'express';
import * as fs from 'fs';
import * as path from 'path';

export type SupportedLanguage = 'ro' | 'en';

// Load translation messages from files
const loadMessages = () => {
  const messagesPath = path.join(__dirname, '../i18n/locales');
  const messages: Record<SupportedLanguage, any> = {} as any;

  try {
    const roMessages = JSON.parse(
      fs.readFileSync(path.join(messagesPath, 'ro.json'), 'utf8')
    );
    const enMessages = JSON.parse(
      fs.readFileSync(path.join(messagesPath, 'en.json'), 'utf8')
    );

    messages.ro = flattenObject(roMessages);
    messages.en = flattenObject(enMessages);
  } catch (error) {
    console.error('Error loading translation files:', error);
    // Fallback to empty objects
    messages.ro = {};
    messages.en = {};
  }

  return messages;
};

// Helper function to flatten nested objects into dot notation
const flattenObject = (obj: any, prefix = ''): Record<string, string> => {
  const flattened: Record<string, string> = {};

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;

      if (typeof obj[key] === 'object' && obj[key] !== null) {
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = obj[key];
      }
    }
  }

  return flattened;
};

const messages = loadMessages();

// Get user's preferred language from request
export const getLanguageFromRequest = (req: Request): SupportedLanguage => {
  // Check for language in headers
  const langHeader = req.headers['accept-language'];
  const langQuery = req.query.lang as string;
  const langBody = req.body?.lang as string;

  // Priority: query param > body > header
  const preferredLang = langQuery || langBody || langHeader;

  if (preferredLang) {
    // Parse language from header like "en-US,en;q=0.9,ro;q=0.8"
    const languages = preferredLang.split(',').map(lang => {
      const [code] = lang.trim().split(';');
      return code.split('-')[0].toLowerCase();
    });

    for (const lang of languages) {
      if (lang === 'en' || lang === 'ro') {
        return lang as SupportedLanguage;
      }
    }
  }

  // Default to Romanian
  return 'ro';
};

// Translate a message key
export const translate = (
  key: string,
  lang: SupportedLanguage = 'ro',
  params?: Record<string, any>
): string => {
  const langMessages = messages[lang] || messages.ro;
  let message = (langMessages as any)[key] || key;

  // Replace parameters in message
  if (params) {
    Object.keys(params).forEach(param => {
      message = message.replace(new RegExp(`{{${param}}}`, 'g'), params[param]);
    });
  }

  return message;
};

// Middleware to add translation function to request
export const i18nMiddleware = (
  req: Request & { t?: Function },
  res: any,
  next: any
) => {
  const lang = getLanguageFromRequest(req);

  req.t = (key: string, params?: Record<string, any>) =>
    translate(key, lang, params);

  next();
};

// Helper function for error messages
export const getErrorMessage = (
  key: string,
  req: Request,
  params?: Record<string, any>
): string => {
  const lang = getLanguageFromRequest(req);
  return translate(key, lang, params);
};
