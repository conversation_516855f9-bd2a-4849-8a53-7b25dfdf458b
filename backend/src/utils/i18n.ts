import { Request } from 'express';

export type SupportedLanguage = 'ro' | 'en';

// Translation messages
const messages = {
  ro: {
    // Authentication messages
    'auth.login.success': 'Autentificare reușită',
    'auth.logout.success': 'Deconectare reușită',
    'auth.token.refreshed': 'Token actualizat cu succes',
    'auth.invalid.credentials': 'Credențiale invalide',
    'auth.account.deactivated': 'Contul este dezactivat',
    'auth.token.expired': 'Token expirat',
    'auth.token.invalid': 'Token invalid',
    'auth.token.required': 'Token de acces necesar',
    'auth.token.refresh.expired': 'Token de reîmprospătare expirat',
    'auth.token.refresh.required': 'Token de reîmprospătare necesar',
    'auth.user.notFound': 'Utilizator negăsit sau inactiv',
    'auth.authentication.failed': 'Autentificare eșuată',
    'auth.authentication.required': 'Autentificare necesară',
    'auth.permissions.insufficient': 'Permisiuni insuficiente',
    'auth.not.authenticated': 'Nu sunteți autentificat',

    // Validation messages
    'validation.errors': 'Erori de validare',
    'validation.username.required': 'Numele de utilizator este obligatoriu',
    'validation.username.length': 'Numele de utilizator trebuie să aibă între 3 și 30 de caractere',
    'validation.username.format': 'Numele de utilizator poate conține doar litere, cifre și underscore',
    'validation.email.required': 'Adresa de email este obligatorie',
    'validation.email.format': 'Adresa de email nu este validă',
    'validation.password.required': 'Parola este obligatorie',
    'validation.password.minLength': 'Parola trebuie să aibă cel puțin 8 caractere',
    'validation.password.strength': 'Parola trebuie să conțină cel puțin o literă mică, o literă mare și o cifră',
    'validation.role.invalid': 'Rolul specificat nu este valid',
    'validation.rateLimitExceeded': 'Prea multe încercări. Încercați din nou mai târziu',

    // General messages
    'general.success': 'Succes',
    'general.error': 'Eroare',
    'general.notFound': 'Nu a fost găsit',
    'general.serverError': 'Eroare internă de server',
    'general.badRequest': 'Cerere invalidă',
    'general.unauthorized': 'Neautorizat',
    'general.forbidden': 'Interzis',
    'general.routeNotFound': 'Ruta nu a fost găsită',

    // API messages
    'api.health.message': 'API-ul Event Manager funcționează',
    'api.user.profile.success': 'Profil utilizator obținut cu succes'
  },
  en: {
    // Authentication messages
    'auth.login.success': 'Login successful',
    'auth.logout.success': 'Logout successful',
    'auth.token.refreshed': 'Token refreshed successfully',
    'auth.invalid.credentials': 'Invalid credentials',
    'auth.account.deactivated': 'Account is deactivated',
    'auth.token.expired': 'Token expired',
    'auth.token.invalid': 'Invalid token',
    'auth.token.required': 'Access token required',
    'auth.token.refresh.expired': 'Refresh token expired',
    'auth.token.refresh.required': 'Refresh token not provided',
    'auth.user.notFound': 'User not found or inactive',
    'auth.authentication.failed': 'Authentication failed',
    'auth.authentication.required': 'Authentication required',
    'auth.permissions.insufficient': 'Insufficient permissions',
    'auth.not.authenticated': 'Not authenticated',

    // Validation messages
    'validation.errors': 'Validation errors',
    'validation.username.required': 'Username is required',
    'validation.username.length': 'Username must be between 3 and 30 characters long',
    'validation.username.format': 'Username can only contain letters, numbers, and underscores',
    'validation.email.required': 'Email is required',
    'validation.email.format': 'Please enter a valid email',
    'validation.password.required': 'Password is required',
    'validation.password.minLength': 'Password must be at least 8 characters long',
    'validation.password.strength': 'Password must contain at least one lowercase letter, one uppercase letter, and one number',
    'validation.role.invalid': 'Invalid role specified',
    'validation.rateLimitExceeded': 'Too many attempts. Please try again later',

    // General messages
    'general.success': 'Success',
    'general.error': 'Error',
    'general.notFound': 'Not found',
    'general.serverError': 'Internal server error',
    'general.badRequest': 'Bad request',
    'general.unauthorized': 'Unauthorized',
    'general.forbidden': 'Forbidden',
    'general.routeNotFound': 'Route not found',

    // API messages
    'api.health.message': 'Event Manager API is running',
    'api.user.profile.success': 'User profile retrieved successfully'
  }
};

// Get user's preferred language from request
export const getLanguageFromRequest = (req: Request): SupportedLanguage => {
  // Check for language in headers
  const langHeader = req.headers['accept-language'];
  const langQuery = req.query.lang as string;
  const langBody = req.body?.lang as string;

  // Priority: query param > body > header
  const preferredLang = langQuery || langBody || langHeader;

  if (preferredLang) {
    // Parse language from header like "en-US,en;q=0.9,ro;q=0.8"
    const languages = preferredLang.split(',').map(lang => {
      const [code] = lang.trim().split(';');
      return code.split('-')[0].toLowerCase();
    });

    for (const lang of languages) {
      if (lang === 'en' || lang === 'ro') {
        return lang as SupportedLanguage;
      }
    }
  }

  // Default to Romanian
  return 'ro';
};

// Translate a message key
export const translate = (key: string, lang: SupportedLanguage = 'ro', params?: Record<string, any>): string => {
  const langMessages = messages[lang] || messages.ro;
  let message = (langMessages as any)[key] || key;

  // Replace parameters in message
  if (params) {
    Object.keys(params).forEach(param => {
      message = message.replace(new RegExp(`{{${param}}}`, 'g'), params[param]);
    });
  }

  return message;
};

// Middleware to add translation function to request
export const i18nMiddleware = (req: Request & { t?: Function }, res: any, next: any) => {
  const lang = getLanguageFromRequest(req);
  
  req.t = (key: string, params?: Record<string, any>) => translate(key, lang, params);
  
  next();
};

// Helper function for error messages
export const getErrorMessage = (key: string, req: Request, params?: Record<string, any>): string => {
  const lang = getLanguageFromRequest(req);
  return translate(key, lang, params);
};
