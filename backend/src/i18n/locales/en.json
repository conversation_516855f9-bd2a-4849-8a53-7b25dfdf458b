{"auth": {"login": {"success": "Login successful"}, "logout": {"success": "Logout successful"}, "token": {"refreshed": "<PERSON><PERSON> refreshed successfully", "expired": "Token expired", "invalid": "Invalid token", "required": "Access token required", "refresh": {"expired": "Refresh token expired", "required": "Refresh token not provided"}}, "invalid": {"credentials": "Invalid credentials"}, "account": {"deactivated": "Account is deactivated"}, "user": {"notFound": "User not found or inactive"}, "authentication": {"failed": "Authentication failed", "required": "Authentication required"}, "permissions": {"insufficient": "Insufficient permissions"}, "not": {"authenticated": "Not authenticated"}}, "validation": {"errors": "Validation errors", "username": {"required": "Username is required", "length": "Username must be between 3 and 30 characters long", "format": "Username can only contain letters, numbers, and underscores"}, "email": {"required": "Email is required", "format": "Please enter a valid email"}, "password": {"required": "Password is required", "minLength": "Password must be at least 8 characters long", "strength": "Password must contain at least one lowercase letter, one uppercase letter, and one number"}, "role": {"invalid": "Invalid role specified"}, "rateLimitExceeded": "Too many attempts. Please try again later"}, "general": {"success": "Success", "error": "Error", "notFound": "Not found", "serverError": "Internal server error", "badRequest": "Bad request", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "routeNotFound": "Route not found"}, "api": {"health": {"message": "Event Manager API is running"}, "user": {"profile": {"success": "User profile retrieved successfully"}}}}