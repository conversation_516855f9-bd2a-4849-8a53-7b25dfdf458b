{"auth": {"login": {"success": "Autentificare reușită"}, "logout": {"success": "Deconectare reușită"}, "token": {"refreshed": "Token actualizat cu succes", "expired": "Token expirat", "invalid": "<PERSON><PERSON> invalid", "required": "Token de acces necesar", "refresh": {"expired": "Token de reîmprospătare expirat", "required": "Token de reîmprospătare necesar"}}, "invalid": {"credentials": "Credențiale invalide"}, "account": {"deactivated": "Contul este dezactivat"}, "user": {"notFound": "Utilizator negăsit sau inactiv"}, "authentication": {"failed": "Autentificare eșuată", "required": "Autentificare necesară"}, "permissions": {"insufficient": "Permisiuni insuficiente"}, "not": {"authenticated": "Nu sunteți autentificat"}}, "validation": {"errors": "E<PERSON><PERSON> valid<PERSON>", "username": {"required": "Numele de utilizator este obligatoriu", "length": "Numele de utilizator trebuie să aibă între 3 și 30 de caractere", "format": "Numele de utilizator poate conține doar litere, cifre și underscore"}, "email": {"required": "Adresa de email este obligatorie", "format": "Adresa de email nu este validă"}, "password": {"required": "Parola este obligatorie", "minLength": "Parola trebuie să aibă cel puțin 8 caractere", "strength": "Parola trebuie să conțină cel puțin o literă mică, o literă mare și o cifră"}, "role": {"invalid": "Rolul specificat nu este valid"}, "rateLimitExceeded": "Prea multe încercări. Încercați din nou mai târziu"}, "general": {"success": "Succes", "error": "Eroare", "notFound": "Nu a fost găsit", "serverError": "Eroare internă de server", "badRequest": "<PERSON><PERSON><PERSON>", "unauthorized": "Neautorizat", "forbidden": "Interzis", "routeNotFound": "Ruta nu a fost găsită"}, "api": {"health": {"message": "API-ul Event Manager funcționează"}, "user": {"profile": {"success": "Profil utilizator obținut cu succes"}}}}