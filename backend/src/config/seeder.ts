import { User } from '../models/User';

export const seedRootUser = async () => {
  try {
    // Check if root user already exists
    const existingRoot = await User.findOne({ username: 'root' });
    
    if (existingRoot) {
      console.log('✅ Root user already exists');
      return;
    }

    // Create root user
    const rootUser = new User({
      username: 'root',
      email: '<EMAIL>',
      password: 'root', // This will be hashed automatically by the pre-save hook
      role: 'admin',
      isActive: true
    });

    await rootUser.save();
    console.log('✅ Root user created successfully');
    console.log('📧 Username: root');
    console.log('🔑 Password: root');
    console.log('⚠️  Please change the default password in production!');
    
  } catch (error) {
    console.error('❌ Error creating root user:', error);
  }
};

export const seedDatabase = async () => {
  console.log('🌱 Seeding database...');
  await seedRootUser();
  console.log('🌱 Database seeding completed');
};
