import { Request, Response, NextFunction } from 'express';
import { User, IUser } from '../models/User';
import { verifyToken, extractTokenFromHeader, JWTPayload } from '../utils/jwt';
import { createError } from './errorHandler';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: IUser;
      tokenPayload?: JWTPayload;
    }
  }
}

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      return next(createError('Access token required', 401));
    }

    // Handle mock tokens for testing when MongoDB is not available
    if (token.startsWith('mock-access-token-')) {
      const mockUser = {
        _id: '507f1f77bcf86cd799439011',
        username: 'root',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        lastLogin: new Date(),
      } as IUser;

      req.user = mockUser;
      req.tokenPayload = {
        userId: mockUser._id as string,
        username: mockUser.username,
        role: mockUser.role,
      };

      return next();
    }

    // Handle real JWT tokens
    const payload = verifyToken(token);

    // Verify user still exists and is active
    const user = await User.findById(payload.userId).select('+password');
    if (!user || !user.isActive) {
      return next(createError('User not found or inactive', 401));
    }

    // Attach user and payload to request
    req.user = user;
    req.tokenPayload = payload;

    next();
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === 'Token expired') {
        return next(createError('Token expired', 401));
      } else if (error.message === 'Invalid token') {
        return next(createError('Invalid token', 401));
      }
    }
    return next(createError('Authentication failed', 401));
  }
};

export const authorize = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createError('Authentication required', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(createError('Insufficient permissions', 403));
    }

    next();
  };
};

// Optional authentication - doesn't fail if no token provided
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);

    if (token) {
      const payload = verifyToken(token);
      const user = await User.findById(payload.userId);

      if (user && user.isActive) {
        req.user = user;
        req.tokenPayload = payload;
      }
    }

    next();
  } catch (error) {
    // Silently continue without authentication
    next();
  }
};
