import { Request, Response, NextFunction } from 'express';
import { getErrorMessage } from '../utils/i18n';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
  data?: any;
}

export const errorHandler = (
  err: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const statusCode = err.statusCode || 500;

  // Translate error message
  let message: string;
  if (err.message.includes('.')) {
    // If message looks like a translation key, translate it
    message = getErrorMessage(err.message, req);
  } else {
    // Otherwise use the message as is
    message = err.message || getErrorMessage('general.serverError', req);
  }

  // Log error for debugging
  console.error('Error:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString(),
    data: err.data,
  });

  // Send error response
  res.status(statusCode).json({
    success: false,
    message,
    ...(err.data && { data: err.data }),
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
  });
};

export const createError = (
  message: string,
  statusCode: number = 500,
  data?: any
): AppError => {
  const error: AppError = new Error(message);
  error.statusCode = statusCode;
  error.isOperational = true;
  error.data = data;
  return error;
};
