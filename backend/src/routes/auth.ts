import { Router } from 'express';
import { 
  login, 
  logout, 
  refreshToken, 
  getProfile, 
  verifyAuth 
} from '../controllers/authController';
import { authenticate } from '../middleware/auth';

const router = Router();

// Public routes
router.post('/login', login);
router.post('/refresh', refreshToken);

// Protected routes
router.post('/logout', authenticate, logout);
router.get('/profile', authenticate, getProfile);
router.get('/verify', authenticate, verifyAuth);

export default router;
