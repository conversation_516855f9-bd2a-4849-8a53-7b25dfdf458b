import { Router } from 'express';
import {
  login,
  logout,
  refreshToken,
  getProfile,
  verifyAuth,
} from '../controllers/authController';
import { authenticate } from '../middleware/auth';
import {
  sanitizeBody,
  loginValidation,
  handleValidationErrors,
  rateLimitValidation,
} from '../middleware/validation';

const router = Router();

// Public routes
router.post(
  '/login',
  rateLimitValidation(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  sanitizeBody,
  loginValidation,
  handleValidationErrors,
  login
);
router.post('/refresh', refreshToken);

// Protected routes
router.post('/logout', authenticate, logout);
router.get('/profile', authenticate, getProfile);
router.get('/verify', authenticate, verifyAuth);

export default router;
