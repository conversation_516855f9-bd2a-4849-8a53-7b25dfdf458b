{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Event Manager Backend API", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "express": "^4.21.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/dompurify": "^3.0.5", "@types/express": "^5.0.2", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.29", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}