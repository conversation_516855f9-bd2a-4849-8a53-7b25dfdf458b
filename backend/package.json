{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Event Manager Backend API", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "mongoose": "^8.15.1", "morgan": "^1.10.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/morgan": "^1.9.9", "@types/node": "^22.15.29", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}